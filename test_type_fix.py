#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试类型转换修复
"""

import sys
import numpy as np

def test_type_conversion():
    """测试类型转换功能"""
    print("测试类型转换修复")
    print("=" * 50)
    
    try:
        from advanced_bayesian_optimizer import AdvancedBayesianOptimizer
        
        # 创建优化器
        optimizer = AdvancedBayesianOptimizer(
            output_dir='test_type_fix_output',
            n_calls=25,
            n_initial_points=10
        )
        
        # 模拟scikit-optimize返回的参数（包含numpy类型）
        test_params = [
            np.int64(2),      # num_layers
            np.int64(128),    # hidden_size  
            np.int64(3),      # kernel_size
            np.int64(1),      # gru_layers
            True,             # bidirectional_gru
            0.3,              # dropout
            0.001,            # learning_rate
            1e-4,             # weight_decay
            np.int64(32),     # batch_size
            'MSELoss',        # loss_type
            'Adam',           # optimizer_type
            np.int64(24),     # sequence_length
            np.int64(10),     # k_best_features
            1.0,              # data_augmentation_strength
            np.int64(5),      # early_stopping_patience
            0.5,              # lr_scheduler_factor
            np.int64(3),      # lr_scheduler_patience
            0.01              # l2_regularization
        ]
        
        print(f"原始参数类型:")
        for i, param in enumerate(test_params[:5]):
            print(f"  {i}: {type(param).__name__} = {param}")
        
        # 测试参数解析
        parsed_params = optimizer._parse_parameters(test_params)
        
        print(f"\n解析后参数类型:")
        for key, value in list(parsed_params.items())[:5]:
            print(f"  {key}: {type(value).__name__} = {value}")
        
        # 验证整数类型参数都是Python原生int
        integer_params = [
            'num_layers', 'hidden_size', 'kernel_size', 'gru_layers', 
            'batch_size', 'sequence_length', 'k_best_features', 
            'early_stopping_patience', 'lr_scheduler_patience'
        ]
        
        print(f"\n整数参数类型验证:")
        all_correct = True
        for param_name in integer_params:
            if param_name in parsed_params:
                value = parsed_params[param_name]
                is_int = isinstance(value, int) and not isinstance(value, np.integer)
                print(f"  {param_name}: {type(value).__name__} {'✓' if is_int else '✗'}")
                if not is_int:
                    all_correct = False
        
        if all_correct:
            print("\n✅ 所有整数参数类型转换正确！")
            return True
        else:
            print("\n❌ 部分参数类型转换失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """测试模型创建是否接受转换后的参数"""
    print("\n" + "=" * 50)
    print("测试模型创建")
    print("=" * 50)
    
    try:
        from model import create_model
        
        # 使用转换后的参数创建模型
        config = {
            'num_layers': int(np.int64(2)),
            'hidden_size': int(np.int64(128)),
            'kernel_size': int(np.int64(3)),
            'gru_layers': int(np.int64(1)),
            'bidirectional': True,
            'dropout': 0.3,
            'input_size': 10,
            'output_size': 1  # 添加缺少的参数
        }
        
        print("测试参数:")
        for key, value in config.items():
            print(f"  {key}: {type(value).__name__} = {value}")
        
        # 尝试创建模型
        model = create_model(config)
        print("\n✅ 模型创建成功！")
        print(f"模型类型: {type(model).__name__}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("类型转换修复测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 类型转换
    if test_type_conversion():
        success_count += 1
    
    # 测试2: 模型创建
    if test_model_creation():
        success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！类型转换修复成功。")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

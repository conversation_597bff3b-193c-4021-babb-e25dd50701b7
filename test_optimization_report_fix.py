#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化报告生成修复
"""

import os
import sys
import pickle
import numpy as np
from advanced_bayesian_optimizer import AdvancedBayesianOptimizer

def test_optimization_report_generation():
    """测试优化报告生成"""
    print("测试优化报告生成修复")
    print("=" * 50)
    
    # 创建测试优化器
    optimizer = AdvancedBayesianOptimizer(
        n_calls=10,
        n_initial_points=5,
        output_dir='test_output'
    )
    
    # 模拟历史数据（包含不同长度的记录）
    test_history = []
    
    # 添加一些完整的记录
    for i in range(20):
        record = {
            'score': 0.1 + np.random.random() * 0.1,
            'params': {
                'num_layers': np.random.randint(1, 4),
                'hidden_size': np.random.choice([32, 64, 128]),
                'kernel_size': np.random.randint(2, 6),
                'gru_layers': np.random.randint(1, 3),
                'dropout': np.random.uniform(0.1, 0.5),
                'learning_rate': np.random.uniform(0.001, 0.01),
                'weight_decay': np.random.uniform(1e-5, 1e-3),
                'batch_size': np.random.choice([16, 32, 64]),
                'loss_type': np.random.choice([0, 1, 2]),
                'optimizer_type': np.random.choice([0, 1, 2]),
                'sequence_length': np.random.randint(10, 50),
                'k_best_features': np.random.randint(5, 25),
                'data_augmentation_strength': np.random.uniform(0.1, 0.9),
                'early_stopping_patience': np.random.randint(5, 20),
                'lr_scheduler_factor': np.random.uniform(0.1, 0.9),
                'lr_scheduler_patience': np.random.randint(2, 10),
                'l2_regularization': np.random.uniform(0.0, 0.01),
                'bidirectional_gru': np.random.choice([True, False])
            },
            'complexity': np.random.random(),
            'stability': np.random.random(),
            'uncertainty': np.random.random()
        }
        test_history.append(record)
    
    # 添加一些不完整的记录（模拟旧版本的数据）
    for i in range(5):
        incomplete_record = {
            'score': 0.15 + np.random.random() * 0.1,
            'params': {
                'num_layers': np.random.randint(1, 4),
                'hidden_size': np.random.choice([32, 64, 128]),
                'kernel_size': np.random.randint(2, 6),
                # 缺少一些参数
            }
        }
        test_history.append(incomplete_record)
    
    # 设置历史数据
    optimizer.optimization_history = test_history
    
    print(f"创建了 {len(test_history)} 条测试历史记录")
    print(f"其中 {len([h for h in test_history if len(h.get('params', {})) > 10])} 条完整记录")
    print(f"其中 {len([h for h in test_history if len(h.get('params', {})) <= 10])} 条不完整记录")
    
    # 测试参数重要性分析
    try:
        print("\n测试参数重要性分析...")
        optimizer._plot_parameter_importance()
        print("✅ 参数重要性分析成功")
    except Exception as e:
        print(f"❌ 参数重要性分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试优化过程可视化
    try:
        print("\n测试优化过程可视化...")
        optimizer._plot_optimization_progress()
        print("✅ 优化过程可视化成功")
    except Exception as e:
        print(f"❌ 优化过程可视化失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试收敛性分析
    try:
        print("\n测试收敛性分析...")
        # 添加收敛数据
        optimizer.convergence_data = [
            {'iteration': i+1, 'best_score': 0.2 - i*0.01, 'current_score': 0.2 + np.random.random()*0.1}
            for i in range(20)
        ]
        optimizer._plot_convergence_analysis()
        print("✅ 收敛性分析成功")
    except Exception as e:
        print(f"❌ 收敛性分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试完整报告生成
    try:
        print("\n测试完整报告生成...")
        
        # 模拟最优参数和结果
        class MockResult:
            def __init__(self):
                self.fun = 0.075
        
        best_params = {
            'num_layers': 2,
            'hidden_size': 64,
            'kernel_size': 2,
            'gru_layers': 1,
            'dropout': 0.1,
            'learning_rate': 0.008,
            'weight_decay': 0.0002,
            'batch_size': 45,
            'loss_type': 'SmoothL1Loss',
            'optimizer_type': 'AdamW'
        }
        
        result = MockResult()
        optimizer._generate_optimization_report(best_params, result)
        print("✅ 完整报告生成成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整报告生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("优化报告生成修复测试")
    print("=" * 60)
    
    # 创建测试输出目录
    os.makedirs('test_output', exist_ok=True)
    
    success = test_optimization_report_generation()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success:
        print("🎉 所有测试通过！优化报告生成修复成功。")
        print("\n修复内容:")
        print("  ✅ 历史数据结构验证")
        print("  ✅ 数组维度一致性检查")
        print("  ✅ 错误处理和日志记录")
        print("  ✅ 不完整记录过滤")
        return True
    else:
        print("⚠️  测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交叉验证类型转换修复
"""

import sys
import numpy as np
from train import convert_params_types
from model import create_model

def test_type_conversion_in_cross_validation():
    """测试交叉验证中的类型转换"""
    print("测试交叉验证类型转换修复")
    print("=" * 50)
    
    # 模拟从贝叶斯优化得到的参数（包含numpy类型）
    best_params = {
        'num_layers': np.int64(2),
        'hidden_size': np.int64(64),
        'kernel_size': np.int64(2),
        'gru_layers': np.int64(1),
        'bidirectional_gru': True,
        'dropout': 0.1,
        'learning_rate': 0.008549927845772659,
        'weight_decay': 0.00020695993766118543,
        'batch_size': np.int64(45),
        'loss_type': 'SmoothL1Loss',
        'optimizer_type': 'AdamW',
        'sequence_length': np.int64(34),
        'k_best_features': np.int64(20),
        'data_augmentation_strength': 0.5,
        'early_stopping_patience': np.int64(12),
        'lr_scheduler_factor': 0.3,
        'lr_scheduler_patience': np.int64(3),
        'l2_regularization': 0.0
    }
    
    print("原始参数中的numpy类型:")
    numpy_params = []
    for key, value in best_params.items():
        if hasattr(value, 'dtype'):
            print(f"  {key}: {type(value).__name__} = {value}")
            numpy_params.append(key)
    
    print(f"\n发现 {len(numpy_params)} 个numpy类型参数")
    
    # 测试类型转换
    try:
        converted_params = convert_params_types(best_params)
        print("\n✅ 参数类型转换成功")
        
        # 验证转换结果
        print("\n转换后的整数参数类型:")
        integer_params = ['num_layers', 'hidden_size', 'kernel_size', 'gru_layers', 
                         'batch_size', 'sequence_length', 'k_best_features', 
                         'early_stopping_patience', 'lr_scheduler_patience']
        
        all_correct = True
        for param in integer_params:
            if param in converted_params:
                value = converted_params[param]
                is_int = isinstance(value, int) and not isinstance(value, np.integer)
                print(f"  {param}: {type(value).__name__} {'✓' if is_int else '✗'}")
                if not is_int:
                    all_correct = False
        
        if all_correct:
            print("\n✅ 所有整数参数类型转换正确")
        else:
            print("\n❌ 部分参数类型转换失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 参数类型转换失败: {e}")
        return False
    
    # 测试模型创建
    try:
        config = {
            'input_size': 10,
            'hidden_size': converted_params['hidden_size'],
            'num_layers': converted_params['num_layers'],
            'output_size': 1,
            'kernel_size': converted_params['kernel_size'],
            'dropout': converted_params['dropout'],
            'gru_layers': converted_params['gru_layers'],
            'bidirectional': converted_params['bidirectional_gru']
        }
        
        model = create_model(config)
        print("\n✅ 模型创建成功")
        print(f"模型类型: {type(model).__name__}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("交叉验证类型转换修复测试")
    print("=" * 60)
    
    success = test_type_conversion_in_cross_validation()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success:
        print("🎉 所有测试通过！交叉验证类型转换修复成功。")
        print("\n现在可以安全运行:")
        print("  python train.py")
        return True
    else:
        print("⚠️  测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强贝叶斯优化器测试脚本

测试新的增强贝叶斯优化功能，包括：
1. 扩展搜索空间
2. 多目标优化
3. 不确定性量化
4. 优化可视化
"""

import os
import sys
import logging
import torch
import numpy as np
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train import EVChargingPredictor
from advanced_bayesian_optimizer import AdvancedBayesianOptimizer
from optimization_visualizer import create_optimization_report

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_advanced_optimizer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_advanced_optimizer():
    """测试增强贝叶斯优化器"""
    
    print("=" * 80)
    print("增强贝叶斯优化器测试")
    print("=" * 80)
    
    # 创建输出目录
    test_output_dir = f"test_advanced_optimizer_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(test_output_dir, exist_ok=True)
    
    try:
        # 1. 初始化预测器
        logging.info("初始化EV充电预测器...")
        predictor = EVChargingPredictor(output_dir=test_output_dir)
        
        # 2. 准备数据（使用较小的数据集进行快速测试）
        logging.info("准备测试数据...")
        train_loader, val_loader, _, _ = predictor.prepare_data(
            'ev_charging_data.csv',
            test_size=0.2,
            val_size=0.2
        )
        
        print(f"训练数据批次数: {len(train_loader)}")
        print(f"验证数据批次数: {len(val_loader)}")
        
        # 3. 测试增强贝叶斯优化器（使用较少的调用次数进行快速测试）
        logging.info("开始增强贝叶斯优化测试...")
        
        best_params = predictor.optimize_hyperparameters_advanced(
            train_loader=train_loader,
            val_loader=val_loader,
            n_calls=30,  # 减少调用次数以加快测试
            enable_multi_objective=True,
            enable_uncertainty_quantification=True
        )
        
        print("\n" + "=" * 50)
        print("增强贝叶斯优化完成!")
        print("=" * 50)
        print("最优参数:")
        for key, value in best_params.items():
            print(f"  {key}: {value}")
        
        # 4. 生成优化报告
        logging.info("生成优化可视化报告...")
        create_optimization_report(test_output_dir)
        
        # 5. 验证最优参数
        logging.info("验证最优参数配置...")
        
        # 获取实际输入维度
        X_sample, _ = next(iter(train_loader))
        actual_input_size = X_sample.shape[2]
        
        config = {
            'input_size': actual_input_size,
            'hidden_size': best_params['hidden_size'],
            'num_layers': best_params['num_layers'],
            'output_size': 1,
            'kernel_size': best_params['kernel_size'],
            'dropout': best_params['dropout'],
            'gru_layers': best_params['gru_layers'],
            'l2_reg': best_params.get('l2_regularization', 1e-4),
            'bidirectional': best_params.get('bidirectional_gru', True),
            'learning_rate': best_params['learning_rate'],
            'weight_decay': best_params['weight_decay']
        }
        
        # 使用最优参数训练一个模型进行验证
        logging.info("使用最优参数训练验证模型...")
        trained_model, model_path, val_loss = predictor.train(
            train_loader, val_loader, config, None, fold_idx=1
        )
        
        print(f"\n验证训练完成:")
        print(f"  模型保存路径: {model_path}")
        print(f"  验证损失: {val_loss:.6f}")
        
        # 6. 对比分析
        logging.info("生成对比分析...")
        
        # 检查生成的文件
        generated_files = []
        for file in os.listdir(test_output_dir):
            if file.endswith(('.png', '.json', '.txt', '.pkl')):
                generated_files.append(file)
        
        print(f"\n生成的文件 ({len(generated_files)} 个):")
        for file in sorted(generated_files):
            file_path = os.path.join(test_output_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"  {file} ({file_size} bytes)")
        
        # 7. 性能总结
        print("\n" + "=" * 50)
        print("测试总结")
        print("=" * 50)
        print("✓ 增强贝叶斯优化器初始化成功")
        print("✓ 扩展搜索空间配置正确")
        print("✓ 多目标优化功能正常")
        print("✓ 不确定性量化功能正常")
        print("✓ 优化可视化报告生成成功")
        print("✓ 最优参数验证通过")
        
        return True
        
    except Exception as e:
        logging.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_space():
    """测试参数空间配置"""
    
    print("\n" + "=" * 50)
    print("参数空间配置测试")
    print("=" * 50)
    
    try:
        optimizer = AdvancedBayesianOptimizer(
            output_dir="temp_test",
            n_calls=10,
            enable_multi_objective=True,
            enable_uncertainty_quantification=True
        )
        
        print(f"搜索空间维度: {len(optimizer.search_space)}")
        print("参数列表:")
        for i, dim in enumerate(optimizer.search_space, 1):
            if hasattr(dim, 'low') and hasattr(dim, 'high'):
                print(f"  {i:2d}. {dim.name}: [{dim.low}, {dim.high}]")
            elif hasattr(dim, 'categories'):
                print(f"  {i:2d}. {dim.name}: {dim.categories}")
            else:
                print(f"  {i:2d}. {dim.name}: {type(dim).__name__}")
        
        print("✓ 参数空间配置正确")
        return True
        
    except Exception as e:
        print(f"✗ 参数空间配置测试失败: {e}")
        return False

def test_visualization():
    """测试可视化功能"""
    
    print("\n" + "=" * 50)
    print("可视化功能测试")
    print("=" * 50)
    
    try:
        # 创建模拟优化历史数据
        test_dir = "temp_visualization_test"
        os.makedirs(test_dir, exist_ok=True)
        
        # 生成模拟数据
        import pickle
        import json
        
        # 模拟优化历史
        history = []
        for i in range(20):
            history.append({
                'params': {
                    'hidden_size': np.random.randint(64, 512),
                    'learning_rate': np.random.uniform(1e-4, 1e-2),
                    'dropout': np.random.uniform(0.1, 0.6),
                    'num_layers': np.random.randint(1, 8),
                    'batch_size': np.random.randint(32, 128)
                },
                'score': np.random.uniform(0.1, 1.0),
                'complexity': np.random.uniform(0.0, 1.0),
                'stability': np.random.uniform(0.0, 1.0),
                'uncertainty': np.random.uniform(0.0, 0.5),
                'final_score': np.random.uniform(0.1, 1.0),
                'timestamp': datetime.now().isoformat()
            })
        
        # 模拟收敛数据
        convergence = []
        best_score = 1.0
        for i in range(20):
            current_score = np.random.uniform(0.1, 1.0)
            if current_score < best_score:
                best_score = current_score
            convergence.append({
                'iteration': i + 1,
                'best_score': best_score,
                'current_score': current_score
            })
        
        # 保存模拟数据
        history_data = {
            'history': history,
            'convergence': convergence,
            'best_params_history': [],
            'final_best_score': best_score
        }
        
        with open(os.path.join(test_dir, 'optimization_history.pkl'), 'wb') as f:
            pickle.dump(history_data, f)
        
        # 模拟最优参数
        best_params = {
            'hidden_size': 256,
            'learning_rate': 0.001,
            'dropout': 0.3,
            'num_layers': 4,
            'batch_size': 64,
            'loss_type': 'MSELoss',
            'optimizer_type': 'Adam'
        }
        
        with open(os.path.join(test_dir, 'best_params_advanced.json'), 'w') as f:
            json.dump(best_params, f, indent=4)
        
        # 测试可视化
        create_optimization_report(test_dir)
        
        # 检查生成的图片
        image_files = [f for f in os.listdir(test_dir) if f.endswith('.png')]
        print(f"生成的可视化图片: {len(image_files)} 个")
        for img in image_files:
            print(f"  ✓ {img}")
        
        # 清理测试文件
        import shutil
        shutil.rmtree(test_dir)
        
        print("✓ 可视化功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 可视化功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    
    print("开始增强贝叶斯优化器全面测试...")
    
    # 检查必要的依赖
    try:
        import torch
        import numpy as np
        import matplotlib.pyplot as plt
        import seaborn as sns
        import pandas as pd
        from skopt import gp_minimize
        print("✓ 所有依赖库检查通过")
    except ImportError as e:
        print(f"✗ 依赖库检查失败: {e}")
        return
    
    # 检查数据文件
    if not os.path.exists('ev_charging_data.csv'):
        print("✗ 找不到数据文件 'ev_charging_data.csv'")
        print("请确保数据文件在当前目录中")
        return
    
    test_results = []
    
    # 1. 测试参数空间配置
    test_results.append(("参数空间配置", test_parameter_space()))
    
    # 2. 测试可视化功能
    test_results.append(("可视化功能", test_visualization()))
    
    # 3. 测试完整的优化流程（可选，耗时较长）
    run_full_test = input("\n是否运行完整的优化测试？(y/N): ").lower().strip() == 'y'
    if run_full_test:
        test_results.append(("完整优化流程", test_advanced_optimizer()))
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20s}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强贝叶斯优化器可以正常使用。")
    else:
        print("⚠️  部分测试失败，请检查错误信息并修复问题。")

if __name__ == '__main__':
    main()

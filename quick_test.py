#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证增强贝叶斯优化功能
"""

import sys
import os

def main():
    print("快速功能测试")
    print("=" * 50)
    
    try:
        # 测试1: 导入模块
        print("1. 测试模块导入...")
        from train import EVChargingPredictor
        from advanced_bayesian_optimizer import AdvancedBayesianOptimizer
        from optimization_visualizer import OptimizationVisualizer
        print("   ✓ 所有模块导入成功")
        
        # 测试2: 创建对象
        print("2. 测试对象创建...")
        predictor = EVChargingPredictor(device='cpu')
        optimizer = AdvancedBayesianOptimizer(
            output_dir='quick_test_output',
            n_calls=25,
            n_initial_points=10
        )
        print("   ✓ 对象创建成功")
        print(f"   搜索空间维度: {len(optimizer.search_space)}")
        
        # 测试3: 数据准备
        print("3. 测试数据准备...")
        if not os.path.exists('ev_charging_data.csv'):
            print("   ✗ 数据文件不存在")
            return False
        
        train_loader, val_loader, test_loader, target_scaler = predictor.prepare_data('ev_charging_data.csv')
        print(f"   ✓ 数据准备成功")
        print(f"   训练集: {len(train_loader)} 批次")
        print(f"   验证集: {len(val_loader)} 批次")
        
        # 测试4: 搜索空间
        print("4. 测试搜索空间...")
        space = optimizer.search_space
        print(f"   参数数量: {len(space)}")
        for i, dim in enumerate(space[:5]):  # 显示前5个参数
            print(f"   {i+1}. {dim.name}: {type(dim).__name__}")
        print("   ...")
        
        print("\n" + "=" * 50)
        print("✓ 所有基础功能测试通过！")
        print("增强贝叶斯优化系统已准备就绪。")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n要运行完整的优化，请执行:")
        print("python train.py")
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强贝叶斯优化集成测试脚本
"""

import sys
import os
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_basic_functionality():
    """测试基础功能"""
    print("=" * 60)
    print("测试1: 基础功能测试")
    print("=" * 60)
    
    try:
        from train import EVChargingPredictor
        from advanced_bayesian_optimizer import AdvancedBayesianOptimizer
        from optimization_visualizer import OptimizationVisualizer
        
        print("✓ 所有模块导入成功")
        
        # 测试预测器创建
        predictor = EVChargingPredictor(device='cpu')
        print("✓ 预测器创建成功")
        
        # 测试优化器创建
        optimizer = AdvancedBayesianOptimizer(
            output_dir='test_integration_output',
            n_calls=25,
            n_initial_points=10
        )
        print("✓ 优化器创建成功")
        print(f"  搜索空间维度: {len(optimizer.search_space)}")
        
        # 测试可视化器创建
        visualizer = OptimizationVisualizer('test_integration_output')
        print("✓ 可视化器创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 基础功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_preparation():
    """测试数据准备"""
    print("\n" + "=" * 60)
    print("测试2: 数据准备测试")
    print("=" * 60)
    
    try:
        from train import EVChargingPredictor
        
        predictor = EVChargingPredictor(device='cpu')
        
        # 检查数据文件是否存在
        if not os.path.exists('ev_charging_data.csv'):
            print("✗ 数据文件 ev_charging_data.csv 不存在")
            return False
        
        print("✓ 数据文件存在")
        
        # 测试数据准备
        train_loader, val_loader, test_loader, target_scaler = predictor.prepare_data('ev_charging_data.csv')
        
        print(f"✓ 数据准备成功")
        print(f"  训练集批次数: {len(train_loader)}")
        print(f"  验证集批次数: {len(val_loader)}")
        print(f"  测试集批次数: {len(test_loader)}")
        
        # 检查数据形状
        for batch_x, batch_y in train_loader:
            print(f"  输入形状: {batch_x.shape}")
            print(f"  输出形状: {batch_y.shape}")
            break
        
        return True, (train_loader, val_loader, test_loader, target_scaler)
        
    except Exception as e:
        print(f"✗ 数据准备测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_optimization():
    """测试优化功能"""
    print("\n" + "=" * 60)
    print("测试3: 优化功能测试")
    print("=" * 60)
    
    try:
        from train import EVChargingPredictor
        
        predictor = EVChargingPredictor(device='cpu')
        train_loader, val_loader, test_loader, target_scaler = predictor.prepare_data('ev_charging_data.csv')
        
        print("开始增强贝叶斯优化测试...")
        
        # 使用较少的调用次数进行快速测试
        best_params = predictor.optimize_hyperparameters_advanced(
            train_loader, val_loader,
            n_calls=25,  # 最小可接受的调用次数
            enable_multi_objective=True,
            enable_uncertainty_quantification=True
        )
        
        print("✓ 增强贝叶斯优化测试成功")
        print(f"  最佳参数数量: {len(best_params)}")
        print(f"  参数名称: {list(best_params.keys())}")
        
        # 检查关键参数
        key_params = ['num_layers', 'hidden_size', 'learning_rate', 'dropout']
        for param in key_params:
            if param in best_params:
                print(f"  {param}: {best_params[param]}")
        
        return True, best_params
        
    except Exception as e:
        print(f"✗ 优化功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_visualization():
    """测试可视化功能"""
    print("\n" + "=" * 60)
    print("测试4: 可视化功能测试")
    print("=" * 60)
    
    try:
        from optimization_visualizer import create_optimization_report
        
        # 检查是否有优化结果
        output_dir = 'test_integration_output'
        if not os.path.exists(output_dir):
            print("✗ 输出目录不存在，跳过可视化测试")
            return False
        
        # 生成可视化报告
        create_optimization_report(output_dir)
        print("✓ 可视化报告生成成功")
        
        # 检查生成的文件
        expected_files = [
            'best_params_advanced.json',
            'optimization_history.pkl'
        ]
        
        for file in expected_files:
            file_path = os.path.join(output_dir, file)
            if os.path.exists(file_path):
                print(f"  ✓ {file} 存在")
            else:
                print(f"  ✗ {file} 不存在")
        
        return True
        
    except Exception as e:
        print(f"✗ 可视化功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("增强贝叶斯优化集成测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试计数器
    total_tests = 4
    passed_tests = 0
    
    # 测试1: 基础功能
    if test_basic_functionality():
        passed_tests += 1
    
    # 测试2: 数据准备
    data_result = test_data_preparation()
    if data_result and data_result[0]:
        passed_tests += 1
    
    # 测试3: 优化功能（如果数据准备成功）
    if data_result and data_result[0]:
        opt_result = test_optimization()
        if opt_result and opt_result[0]:
            passed_tests += 1
        
        # 测试4: 可视化功能（如果优化成功）
        if opt_result and opt_result[0]:
            if test_visualization():
                passed_tests += 1
    else:
        print("\n跳过优化和可视化测试（数据准备失败）")
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！增强贝叶斯优化系统工作正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
